{"expo": {"name": "WakeMeGo", "slug": "WakeMeGo", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#FF7EB3"}, "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs location access to track your position and trigger alarms when you reach your destination.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs background location access to monitor geofences and wake you up when you reach your destination.", "UIBackgroundModes": ["location", "background-processing"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FF7EB3"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_BACKGROUND_LOCATION", "android.permission.FOREGROUND_SERVICE", "android.permission.WAKE_LOCK", "android.permission.VIBRATE", "android.permission.ACCESS_COARSE_LOCATION"], "package": "com.ronan15.WakeMeGo"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow WakeMeGo to use your location to track your journey and trigger alarms when you reach your destination.", "locationAlwaysPermission": "Allow WakeMeGo to use your location in the background to monitor geofences and wake you up when you reach your destination.", "locationWhenInUsePermission": "Allow WakeMeGo to use your location to track your journey and show your position on the map."}], ["expo-notifications", {"icon": "./assets/icon.png", "color": "#FF7EB3"}]], "extra": {"eas": {"projectId": "95c60e38-ddb5-4cd4-89f4-116921a0cb71"}}}}